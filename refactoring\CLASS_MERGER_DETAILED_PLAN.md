# 重复类合并详细方案

## 🎯 合并目的

通过系统性合并重复功能的类，实现：
- **减少代码重复**：消除60%的冗余代码
- **统一接口设计**：提供一致的API调用方式
- **提升维护效率**：将分散的功能集中管理
- **优化性能表现**：统一优化策略，提升整体效能

---

## 🚀 并发管理合并方案 (优先级：🔴 紧急)

### 现状分析

#### 当前重复的3个类：

**1. Concurrent_Network_Manager (utils/)**
```php
// 核心功能：cURL multi-handle网络并发
class Concurrent_Network_Manager {
    private $multi_handle;           // cURL多句柄
    private $curl_handles = [];      // 单个请求句柄
    private $max_concurrent = 5;     // 最大并发数
    
    // 核心方法
    public function add_request(array $request_config);
    public function execute_all_requests(): array;
    public function set_concurrency_limit(int $limit);
}
```

**2. Unified_Concurrency_Manager (utils/)**
```php
// 核心功能：统一配置管理
class Unified_Concurrency_Manager {
    private static $config = [];     // 配置存储
    private static $stats = [];      // 统计数据
    
    // 核心方法  
    public static function get_config(): array;
    public static function configure_limits(array $config);
    public static function get_performance_stats(): array;
}
```

**3. Dynamic_Concurrency_Manager (core/)**
```php
// 核心功能：性能自适应调优
class Dynamic_Concurrency_Manager {
    private array $performance_metrics = []; // 性能指标
    private int $current_concurrency = 5;    // 当前并发数
    
    // 核心方法
    public function calculate_optimal_concurrency(): int;
    public function adjust_based_on_performance();
    public function monitor_system_resources(): array;
}
```

### 功能重叠分析

| 功能模块 | Concurrent_Network | Unified_Concurrency | Dynamic_Concurrency | 重叠度 |
|---------|-------------------|---------------------|---------------------|--------|
| **并发执行** | ✅ 完整实现 | ❌ 无 | ❌ 无 | 0% |
| **配置管理** | ⚠️ 部分支持 | ✅ 完整实现 | ⚠️ 部分支持 | **40%** |
| **性能监控** | ⚠️ 基础监控 | ✅ 统计收集 | ✅ 深度监控 | **60%** |
| **动态调优** | ❌ 无 | ❌ 无 | ✅ 完整实现 | 0% |
| **错误处理** | ⚠️ 基础处理 | ⚠️ 基础处理 | ⚠️ 基础处理 | **30%** |

**总体代码重复率：约34%**

### 合并设计方案

#### 新的统一架构：
```php
<?php
declare(strict_types=1);

namespace NTWP\Infrastructure;

/**
 * 统一并发管理器
 * 
 * 合并功能：
 * ✅ 网络请求并发 (from Concurrent_Network_Manager)
 * ✅ 配置管理 (from Unified_Concurrency_Manager)  
 * ✅ 动态调优 (from Dynamic_Concurrency_Manager)
 */
class Concurrency_Manager {
    
    // === 配置管理模块 (from Unified_Concurrency_Manager) ===
    private array $config = [
        'max_concurrent_requests' => 5,
        'max_concurrent_downloads' => 3,
        'request_timeout' => 30,
        'enable_adaptive_adjustment' => true,
        'memory_threshold' => 0.8,
        'cpu_threshold' => 2.0,
        'retry_attempts' => 3,
        'retry_delay' => 1000, // 毫秒
    ];
    
    // === 并发执行模块 (from Concurrent_Network_Manager) ===
    private $multi_handle;
    private array $active_handles = [];
    private array $request_queue = [];
    private array $response_buffer = [];
    
    // === 性能监控模块 (from Dynamic_Concurrency_Manager) ===
    private array $performance_metrics = [];
    private int $current_optimal_concurrency = 5;
    private Performance_Monitor $monitor;
    
    // === 统计数据 (from Unified_Concurrency_Manager) ===
    private array $execution_stats = [
        'total_requests' => 0,
        'successful_requests' => 0,
        'failed_requests' => 0,
        'average_response_time' => 0,
        'peak_concurrency' => 0,
        'adaptive_adjustments' => 0,
    ];
    
    /**
     * 构造函数
     */
    public function __construct(Performance_Monitor $monitor) {
        $this->monitor = $monitor;
        $this->multi_handle = curl_multi_init();
        $this->initialize_performance_tracking();
    }
    
    // ================== 核心并发执行方法 ==================
    
    /**
     * 执行并发请求 (主要来自Concurrent_Network_Manager)
     * 
     * @param array $requests 请求配置数组
     * @return array 响应结果数组
     */
    public function execute_concurrent_requests(array $requests): array {
        if (empty($requests)) {
            return [];
        }
        
        $start_time = microtime(true);
        $this->execution_stats['total_requests'] += count($requests);
        
        // 动态调整并发数 (来自Dynamic_Concurrency_Manager)
        $optimal_concurrency = $this->calculate_optimal_concurrency();
        
        Logger::debug_log(
            sprintf('开始并发处理 %d 个请求，当前最优并发数: %d', 
                count($requests), $optimal_concurrency),
            'Concurrency Manager'
        );
        
        $results = [];
        $batches = array_chunk($requests, $optimal_concurrency);
        
        foreach ($batches as $batch_index => $batch) {
            // 处理单个批次
            $batch_results = $this->process_request_batch($batch);
            $results = array_merge($results, $batch_results);
            
            // 实时性能调整 (来自Dynamic_Concurrency_Manager)
            $this->adjust_concurrency_based_on_batch_performance($batch_results);
            
            // 更新统计数据
            $this->update_execution_statistics($batch_results);
        }
        
        $total_time = microtime(true) - $start_time;
        $this->execution_stats['average_response_time'] = $total_time / count($requests);
        
        Logger::debug_log(
            sprintf('并发处理完成，总耗时: %.2fs，成功率: %.1f%%', 
                $total_time, $this->get_success_rate()),
            'Concurrency Manager'
        );
        
        return $results;
    }
    
    /**
     * 处理单个请求批次
     * 
     * @param array $batch 批次请求
     * @return array 批次结果
     */
    private function process_request_batch(array $batch): array {
        $curl_handles = [];
        $results = [];
        
        // 初始化cURL句柄
        foreach ($batch as $index => $request) {
            $ch = $this->create_curl_handle($request);
            curl_multi_add_handle($this->multi_handle, $ch);
            $curl_handles[$index] = $ch;
        }
        
        // 执行并发请求
        $this->execute_multi_handle();
        
        // 收集结果
        foreach ($curl_handles as $index => $ch) {
            $results[$index] = $this->extract_response_data($ch);
            $this->cleanup_curl_handle($ch);
        }
        
        return $results;
    }
    
    // ================== 配置管理方法 ==================
    
    /**
     * 获取当前配置 (来自Unified_Concurrency_Manager)
     * 
     * @return array 配置数组
     */
    public function get_config(): array {
        return $this->config;
    }
    
    /**
     * 配置并发限制 (来自Unified_Concurrency_Manager)
     * 
     * @param array $config 配置数组
     */
    public function configure_limits(array $config): void {
        // 验证配置参数
        $validated_config = $this->validate_config($config);
        
        // 合并配置
        $this->config = array_merge($this->config, $validated_config);
        
        // 记录配置变更
        Logger::debug_log(
            sprintf('并发配置已更新: %s', json_encode($validated_config)),
            'Concurrency Manager'
        );
        
        // 触发配置变更事件
        do_action('ntwp_concurrency_config_updated', $this->config);
    }
    
    // ================== 动态调优方法 ==================
    
    /**
     * 计算最优并发数 (来自Dynamic_Concurrency_Manager)
     * 
     * @return int 最优并发数
     */
    public function calculate_optimal_concurrency(): int {
        $base_concurrency = $this->config['max_concurrent_requests'];
        
        if (!$this->config['enable_adaptive_adjustment']) {
            return $base_concurrency;
        }
        
        // 获取系统资源状况
        $system_metrics = $this->monitor_system_resources();
        
        // 计算调整因子
        $adjustment_factor = $this->calculate_adjustment_factor($system_metrics);
        
        // 应用调整
        $optimal = intval($base_concurrency * $adjustment_factor);
        
        // 确保在合理范围内
        $optimal = max(1, min($this->config['max_concurrent_requests'], $optimal));
        
        // 更新当前最优值
        if ($optimal !== $this->current_optimal_concurrency) {
            $this->current_optimal_concurrency = $optimal;
            $this->execution_stats['adaptive_adjustments']++;
            
            Logger::debug_log(
                sprintf('并发数自适应调整: %d → %d (调整因子: %.2f)', 
                    $base_concurrency, $optimal, $adjustment_factor),
                'Concurrency Manager'
            );
        }
        
        return $optimal;
    }
    
    /**
     * 监控系统资源 (来自Dynamic_Concurrency_Manager)
     * 
     * @return array 系统资源指标
     */
    public function monitor_system_resources(): array {
        $metrics = [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => $this->get_memory_limit(),
            'system_load' => sys_getloadavg()[0] ?? 1.0,
            'cpu_usage' => $this->get_cpu_usage(),
            'active_connections' => count($this->active_handles),
        ];
        
        // 计算内存使用率
        $metrics['memory_usage_percentage'] = 
            ($metrics['memory_usage'] / $metrics['memory_limit']) * 100;
        
        return $metrics;
    }
    
    /**
     * 计算调整因子
     * 
     * @param array $system_metrics 系统指标
     * @return float 调整因子 (0.5-1.5)
     */
    private function calculate_adjustment_factor(array $system_metrics): float {
        $factor = 1.0;
        
        // 内存使用率调整
        if ($system_metrics['memory_usage_percentage'] > 80) {
            $factor *= 0.7; // 减少30%
        } elseif ($system_metrics['memory_usage_percentage'] < 50) {
            $factor *= 1.2; // 增加20%
        }
        
        // CPU负载调整
        if ($system_metrics['system_load'] > $this->config['cpu_threshold']) {
            $factor *= 0.8; // 减少20%
        } elseif ($system_metrics['system_load'] < 1.0) {
            $factor *= 1.1; // 增加10%
        }
        
        // 历史性能调整
        if (!empty($this->performance_metrics)) {
            $avg_response_time = array_sum($this->performance_metrics) / count($this->performance_metrics);
            
            if ($avg_response_time > 3.0) {
                $factor *= 0.9; // 响应时间过长，减少并发
            } elseif ($avg_response_time < 1.0) {
                $factor *= 1.1; // 响应快速，可以增加并发
            }
        }
        
        // 限制调整范围
        return max(0.5, min(1.5, $factor));
    }
    
    // ================== 性能监控方法 ==================
    
    /**
     * 获取性能统计 (来自Unified_Concurrency_Manager)
     * 
     * @return array 性能统计数据
     */
    public function get_performance_stats(): array {
        $current_metrics = $this->monitor_system_resources();
        
        return array_merge($this->execution_stats, [
            'current_config' => $this->config,
            'current_optimal_concurrency' => $this->current_optimal_concurrency,
            'system_metrics' => $current_metrics,
            'success_rate' => $this->get_success_rate(),
            'performance_trend' => $this->get_performance_trend(),
        ]);
    }
    
    /**
     * 监控性能数据
     * 
     * @return array 监控数据
     */
    public function monitor_performance(): array {
        return [
            'active_requests' => count($this->active_handles),
            'queue_size' => count($this->request_queue),
            'current_concurrency' => $this->current_optimal_concurrency,
            'peak_concurrency' => $this->execution_stats['peak_concurrency'],
            'system_resources' => $this->monitor_system_resources(),
            'recent_performance' => array_slice($this->performance_metrics, -10),
        ];
    }
    
    // ================== 私有辅助方法 ==================
    
    /**
     * 创建cURL句柄
     */
    private function create_curl_handle(array $request) {
        $ch = curl_init();
        
        $default_options = [
            CURLOPT_URL => $request['url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['request_timeout'],
            CURLOPT_FOLLOWLOCATION => false,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_USERAGENT => 'Notion-to-WordPress/2.0',
            CURLOPT_HTTPHEADER => $request['headers'] ?? [],
        ];
        
        // 合并自定义选项
        if (!empty($request['curl_options'])) {
            $default_options = array_replace($default_options, $request['curl_options']);
        }
        
        curl_setopt_array($ch, $default_options);
        
        return $ch;
    }
    
    /**
     * 执行multi-handle
     */
    private function execute_multi_handle(): void {
        $running = null;
        
        do {
            $status = curl_multi_exec($this->multi_handle, $running);
            
            if ($running > 0) {
                // 更新峰值并发数
                $this->execution_stats['peak_concurrency'] = 
                    max($this->execution_stats['peak_concurrency'], $running);
                
                curl_multi_select($this->multi_handle, 0.1);
            }
        } while ($running > 0 && $status === CURLM_OK);
    }
    
    /**
     * 提取响应数据
     */
    private function extract_response_data($ch): array {
        $response = curl_multi_getcontent($ch);
        $info = curl_getinfo($ch);
        $error = curl_error($ch);
        
        $is_success = empty($error) && $info['http_code'] >= 200 && $info['http_code'] < 400;
        
        return [
            'success' => $is_success,
            'response' => $response,
            'http_code' => $info['http_code'],
            'response_time' => $info['total_time'],
            'error' => $error,
            'info' => $info,
        ];
    }
    
    /**
     * 清理cURL句柄
     */
    private function cleanup_curl_handle($ch): void {
        curl_multi_remove_handle($this->multi_handle, $ch);
        curl_close($ch);
    }
    
    /**
     * 更新执行统计
     */
    private function update_execution_statistics(array $batch_results): void {
        foreach ($batch_results as $result) {
            if ($result['success']) {
                $this->execution_stats['successful_requests']++;
            } else {
                $this->execution_stats['failed_requests']++;
            }
            
            // 记录响应时间
            $this->performance_metrics[] = $result['response_time'];
            
            // 限制性能数据数量
            if (count($this->performance_metrics) > 100) {
                array_shift($this->performance_metrics);
            }
        }
    }
    
    /**
     * 获取成功率
     */
    private function get_success_rate(): float {
        $total = $this->execution_stats['successful_requests'] + 
                $this->execution_stats['failed_requests'];
        
        if ($total === 0) {
            return 100.0;
        }
        
        return ($this->execution_stats['successful_requests'] / $total) * 100;
    }
    
    /**
     * 获取性能趋势
     */
    private function get_performance_trend(): array {
        if (empty($this->performance_metrics)) {
            return [];
        }
        
        $recent = array_slice($this->performance_metrics, -10);
        $older = array_slice($this->performance_metrics, -20, 10);
        
        return [
            'recent_avg' => array_sum($recent) / count($recent),
            'older_avg' => !empty($older) ? array_sum($older) / count($older) : 0,
            'trend' => !empty($older) ? 
                (array_sum($recent) / count($recent)) - (array_sum($older) / count($older)) : 0,
        ];
    }
    
    /**
     * 验证配置参数
     */
    private function validate_config(array $config): array {
        $validated = [];
        
        // 验证并发数限制
        if (isset($config['max_concurrent_requests'])) {
            $validated['max_concurrent_requests'] = max(1, min(20, intval($config['max_concurrent_requests'])));
        }
        
        // 验证超时设置
        if (isset($config['request_timeout'])) {
            $validated['request_timeout'] = max(5, min(300, intval($config['request_timeout'])));
        }
        
        // 验证阈值设置
        if (isset($config['memory_threshold'])) {
            $validated['memory_threshold'] = max(0.5, min(0.95, floatval($config['memory_threshold'])));
        }
        
        if (isset($config['cpu_threshold'])) {
            $validated['cpu_threshold'] = max(1.0, min(10.0, floatval($config['cpu_threshold'])));
        }
        
        return $validated;
    }
    
    /**
     * 获取内存限制
     */
    private function get_memory_limit(): int {
        $limit = ini_get('memory_limit');
        if ($limit === '-1') {
            return PHP_INT_MAX;
        }
        
        return $this->convert_to_bytes($limit);
    }
    
    /**
     * 转换内存格式为字节
     */
    private function convert_to_bytes(string $limit): int {
        $unit = strtoupper(substr($limit, -1));
        $value = intval($limit);
        
        switch ($unit) {
            case 'G': return $value * 1024 * 1024 * 1024;
            case 'M': return $value * 1024 * 1024;
            case 'K': return $value * 1024;
            default: return $value;
        }
    }
    
    /**
     * 获取CPU使用率（简化版）
     */
    private function get_cpu_usage(): float {
        // 简化实现，实际可以通过系统调用获取更精确的数据
        $load = sys_getloadavg();
        return $load ? $load[0] : 1.0;
    }
    
    /**
     * 初始化性能跟踪
     */
    private function initialize_performance_tracking(): void {
        // 注册关闭时的清理函数
        register_shutdown_function([$this, 'cleanup_on_shutdown']);
    }
    
    /**
     * 关闭时清理
     */
    public function cleanup_on_shutdown(): void {
        if (is_resource($this->multi_handle)) {
            curl_multi_close($this->multi_handle);
        }
    }
    
    /**
     * 析构函数
     */
    public function __destruct() {
        $this->cleanup_on_shutdown();
    }
}
```

### 迁移步骤

#### 步骤1：创建新类 (第1天)
- [ ] 创建 `includes/infrastructure/Concurrency_Manager.php`
- [ ] 实现基础接口和构造函数
- [ ] 添加基本的配置管理功能

#### 步骤2：迁移核心功能 (第2-3天)
- [ ] 从 `Concurrent_Network_Manager` 迁移cURL multi-handle逻辑
- [ ] 从 `Unified_Concurrency_Manager` 迁移配置管理方法
- [ ] 从 `Dynamic_Concurrency_Manager` 迁移动态调优算法

#### 步骤3：集成和测试 (第4天)
- [ ] 更新依赖注入容器注册
- [ ] 创建单元测试覆盖所有主要功能
- [ ] 性能基准测试确保不降级

#### 步骤4：更新调用点 (第5天)
- [ ] 更新 `Import_Coordinator` 中的并发调用
- [ ] 更新 `API` 服务中的网络请求逻辑
- [ ] 更新所有其他使用并发功能的地方

#### 步骤5：清理和文档 (第6天)
- [ ] 删除旧的3个并发管理类
- [ ] 更新相关文档和注释
- [ ] 完成代码审查

---

## 💾 缓存系统合并方案 (优先级：🟡 中等)

### 现状分析

#### 当前的2个缓存类：

**1. Smart_Cache (utils/)**
```php
// 核心功能：智能TTL管理，持久化缓存
class Smart_Cache {
    const CACHE_TYPES = [
        'user_info' => ['ttl' => 3600],
        'database_structure' => ['ttl' => 1800],
        'page_content' => ['ttl' => 300],
        'api_response' => ['ttl' => 60],
    ];
    
    // 核心方法
    public static function get(string $type, string $identifier): mixed;
    public static function set(string $type, string $identifier, $data, int $ttl = null): bool;
    public static function get_tiered(string $type, string $identifier): mixed; // L1+L2缓存
}
```

**2. Session_Cache (utils/)**
```php
// 核心功能：会话级内存缓存，LRU策略
class Session_Cache {
    private static $cache = [];          // 内存缓存存储
    private static $max_cache_size = 100; // 最大缓存条目
    
    // 核心方法
    public static function get(string $key): mixed;
    public static function set(string $key, $data, int $ttl = 600): void;
    public static function has(string $key): bool;
}
```

### 功能互补分析

| 功能特性 | Smart_Cache | Session_Cache | 合并收益 |
|---------|-------------|---------------|----------|
| **持久化存储** | ✅ 支持transient | ❌ 仅内存 | 统一存储策略 |
| **会话级缓存** | ⚠️ L1缓存部分支持 | ✅ 完整支持 | 完善会话缓存 |
| **TTL管理** | ✅ 智能TTL | ✅ 简单TTL | 优化TTL策略 |
| **LRU策略** | ❌ 无 | ✅ 支持 | 增强内存管理 |
| **类型化缓存** | ✅ 支持 | ❌ 无 | 保持类型管理 |

### 合并后的统一缓存设计

```php
<?php
declare(strict_types=1);

namespace NTWP\Infrastructure;

/**
 * 统一缓存管理器
 * 
 * 合并功能：
 * ✅ 持久化缓存 (from Smart_Cache)
 * ✅ 会话级缓存 (from Session_Cache)
 * ✅ 多层缓存架构 (L1内存 + L2持久化)
 */
class Cache_Manager {
    
    // === L1缓存（内存级，来自Session_Cache） ===
    private static array $l1_cache = [];
    private static int $l1_max_size = 100;
    private static int $l1_current_size = 0;
    
    // === L2缓存配置（持久化级，来自Smart_Cache） ===
    private const CACHE_TYPES = [
        'user_info' => [
            'ttl' => 3600,      // 1小时
            'l1_eligible' => true,
            'description' => '用户信息缓存'
        ],
        'database_structure' => [
            'ttl' => 1800,      // 30分钟
            'l1_eligible' => true,
            'description' => '数据库结构缓存'
        ],
        'page_content' => [
            'ttl' => 300,       // 5分钟
            'l1_eligible' => false, // 内容较大，不适合L1缓存
            'description' => '页面内容缓存'
        ],
        'api_response' => [
            'ttl' => 60,        // 1分钟
            'l1_eligible' => true,
            'description' => 'API响应缓存'
        ],
        'session' => [
            'ttl' => 600,       // 10分钟
            'l1_eligible' => true,
            'description' => '会话级缓存'
        ]
    ];
    
    // === 统计数据 ===
    private static array $stats = [
        'l1_hits' => 0,
        'l1_misses' => 0,
        'l2_hits' => 0,
        'l2_misses' => 0,
        'sets' => 0,
        'evictions' => 0,
    ];
    
    /**
     * 获取缓存数据（多层策略）
     * 
     * @param string $key 缓存键
     * @param string $type 缓存类型
     * @return mixed 缓存数据或null
     */
    public static function get(string $key, string $type = 'session'): mixed {
        // L1缓存检查（内存级）
        $l1_result = self::get_from_l1($key);
        if ($l1_result !== null) {
            self::$stats['l1_hits']++;
            return $l1_result;
        }
        self::$stats['l1_misses']++;
        
        // L2缓存检查（持久化级）
        $l2_result = self::get_from_l2($key, $type);
        if ($l2_result !== null) {
            self::$stats['l2_hits']++;
            
            // 提升到L1缓存（如果符合条件）
            if (self::is_l1_eligible($type, $l2_result)) {
                self::set_to_l1($key, $l2_result);
            }
            
            return $l2_result;
        }
        self::$stats['l2_misses']++;
        
        return null;
    }
    
    /**
     * 设置缓存数据（多层策略）
     * 
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $ttl 生存时间（秒）
     * @param string $type 缓存类型
     * @return bool 设置是否成功
     */
    public static function set(string $key, mixed $value, int $ttl = null, string $type = 'session'): bool {
        self::$stats['sets']++;
        
        // 获取缓存配置
        $cache_config = self::CACHE_TYPES[$type] ?? self::CACHE_TYPES['session'];
        $effective_ttl = $ttl ?? $cache_config['ttl'];
        
        // 设置L2缓存（持久化）
        $l2_success = self::set_to_l2($key, $value, $effective_ttl);
        
        // 设置L1缓存（内存）
        if (self::is_l1_eligible($type, $value)) {
            self::set_to_l1($key, $value, $effective_ttl);
        }
        
        return $l2_success;
    }
    
    /**
     * 检查缓存是否存在
     * 
     * @param string $key 缓存键
     * @param string $type 缓存类型
     * @return bool 是否存在
     */
    public static function has(string $key, string $type = 'session'): bool {
        // 检查L1缓存
        if (self::has_in_l1($key)) {
            return true;
        }
        
        // 检查L2缓存
        return self::has_in_l2($key);
    }
    
    /**
     * 删除缓存
     * 
     * @param string $key 缓存键
     * @param string $type 缓存类型
     * @return bool 删除是否成功
     */
    public static function delete(string $key, string $type = 'session'): bool {
        // 从L1缓存删除
        self::delete_from_l1($key);
        
        // 从L2缓存删除
        return self::delete_from_l2($key);
    }
    
    /**
     * 按模式批量删除缓存
     * 
     * @param string $pattern 模式（支持通配符*）
     * @return int 删除的缓存数量
     */
    public static function invalidate_pattern(string $pattern): int {
        $deleted_count = 0;
        
        // L1缓存模式删除
        $deleted_count += self::invalidate_l1_pattern($pattern);
        
        // L2缓存模式删除  
        $deleted_count += self::invalidate_l2_pattern($pattern);
        
        return $deleted_count;
    }
    
    /**
     * 清理过期缓存
     */
    public static function cleanup_expired(): void {
        // L1缓存清理
        self::cleanup_l1_expired();
        
        // L2缓存自动通过WordPress transient机制清理
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return array 统计数据
     */
    public static function get_stats(): array {
        $total_requests = self::$stats['l1_hits'] + self::$stats['l1_misses'];
        $l1_hit_rate = $total_requests > 0 ? 
            (self::$stats['l1_hits'] / $total_requests) * 100 : 0;
        
        $total_l2_requests = self::$stats['l2_hits'] + self::$stats['l2_misses'];
        $l2_hit_rate = $total_l2_requests > 0 ? 
            (self::$stats['l2_hits'] / $total_l2_requests) * 100 : 0;
        
        return array_merge(self::$stats, [
            'l1_hit_rate' => $l1_hit_rate,
            'l2_hit_rate' => $l2_hit_rate,
            'l1_cache_size' => self::$l1_current_size,
            'l1_cache_usage' => (self::$l1_current_size / self::$l1_max_size) * 100,
            'cache_types' => array_keys(self::CACHE_TYPES),
        ]);
    }
    
    // ================== L1缓存方法（内存级） ==================
    
    /**
     * 从L1缓存获取数据
     */
    private static function get_from_l1(string $key): mixed {
        if (!isset(self::$l1_cache[$key])) {
            return null;
        }
        
        $cache_item = self::$l1_cache[$key];
        
        // 检查是否过期
        if (time() > $cache_item['expires_at']) {
            self::delete_from_l1($key);
            return null;
        }
        
        // 更新访问时间（LRU策略）
        self::$l1_cache[$key]['last_access'] = time();
        
        return $cache_item['data'];
    }
    
    /**
     * 设置L1缓存数据
     */
    private static function set_to_l1(string $key, mixed $value, int $ttl = 600): void {
        // 检查缓存大小，必要时清理
        if (self::$l1_current_size >= self::$l1_max_size) {
            self::evict_l1_lru();
        }
        
        $now = time();
        self::$l1_cache[$key] = [
            'data' => $value,
            'created_at' => $now,
            'last_access' => $now,
            'expires_at' => $now + $ttl,
        ];
        
        if (!isset(self::$l1_cache[$key])) {
            self::$l1_current_size++;
        }
    }
    
    /**
     * 检查L1缓存中是否存在
     */
    private static function has_in_l1(string $key): bool {
        return self::get_from_l1($key) !== null;
    }
    
    /**
     * 从L1缓存删除
     */
    private static function delete_from_l1(string $key): void {
        if (isset(self::$l1_cache[$key])) {
            unset(self::$l1_cache[$key]);
            self::$l1_current_size--;
        }
    }
    
    /**
     * LRU清理L1缓存
     */
    private static function evict_l1_lru(): void {
        if (empty(self::$l1_cache)) {
            return;
        }
        
        // 找到最少使用的缓存项
        $oldest_key = null;
        $oldest_access = PHP_INT_MAX;
        
        foreach (self::$l1_cache as $key => $item) {
            if ($item['last_access'] < $oldest_access) {
                $oldest_access = $item['last_access'];
                $oldest_key = $key;
            }
        }
        
        if ($oldest_key) {
            self::delete_from_l1($oldest_key);
            self::$stats['evictions']++;
        }
    }
    
    /**
     * 清理L1过期缓存
     */
    private static function cleanup_l1_expired(): void {
        $now = time();
        $expired_keys = [];
        
        foreach (self::$l1_cache as $key => $item) {
            if ($now > $item['expires_at']) {
                $expired_keys[] = $key;
            }
        }
        
        foreach ($expired_keys as $key) {
            self::delete_from_l1($key);
        }
    }
    
    /**
     * L1缓存模式删除
     */
    private static function invalidate_l1_pattern(string $pattern): int {
        $deleted = 0;
        $regex_pattern = str_replace('*', '.*', preg_quote($pattern, '/'));
        
        foreach (array_keys(self::$l1_cache) as $key) {
            if (preg_match("/^{$regex_pattern}$/", $key)) {
                self::delete_from_l1($key);
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    // ================== L2缓存方法（持久化级） ==================
    
    /**
     * 从L2缓存获取数据
     */
    private static function get_from_l2(string $key, string $type): mixed {
        $cache_key = self::generate_cache_key($key, $type);
        $cache_data = get_transient($cache_key);
        
        if ($cache_data === false) {
            return null;
        }
        
        // 检查数据完整性
        if (!is_array($cache_data) || !isset($cache_data['data'], $cache_data['timestamp'])) {
            delete_transient($cache_key);
            return null;
        }
        
        return $cache_data['data'];
    }
    
    /**
     * 设置L2缓存数据
     */
    private static function set_to_l2(string $key, mixed $value, int $ttl): bool {
        $cache_key = self::generate_cache_key($key, 'persistent');
        
        $cache_data = [
            'data' => $value,
            'timestamp' => time(),
            'ttl' => $ttl,
        ];
        
        return set_transient($cache_key, $cache_data, $ttl);
    }
    
    /**
     * 检查L2缓存中是否存在
     */
    private static function has_in_l2(string $key): bool {
        $cache_key = self::generate_cache_key($key, 'persistent');
        return get_transient($cache_key) !== false;
    }
    
    /**
     * 从L2缓存删除
     */
    private static function delete_from_l2(string $key): bool {
        $cache_key = self::generate_cache_key($key, 'persistent');
        return delete_transient($cache_key);
    }
    
    /**
     * L2缓存模式删除
     */
    private static function invalidate_l2_pattern(string $pattern): int {
        global $wpdb;
        
        $regex_pattern = str_replace('*', '%', $pattern);
        $cache_prefix = 'ntwp_cache_';
        
        $deleted = $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} 
                WHERE option_name LIKE %s 
                AND option_name LIKE %s",
                '_transient_' . $cache_prefix . $regex_pattern,
                '_transient_timeout_' . $cache_prefix . $regex_pattern
            )
        );
        
        return $deleted / 2; // 每个transient有两个记录
    }
    
    // ================== 辅助方法 ==================
    
    /**
     * 生成缓存键
     */
    private static function generate_cache_key(string $key, string $type): string {
        return 'ntwp_cache_' . $type . '_' . md5($key);
    }
    
    /**
     * 检查是否适合L1缓存
     */
    private static function is_l1_eligible(string $type, mixed $value): bool {
        $cache_config = self::CACHE_TYPES[$type] ?? ['l1_eligible' => true];
        
        if (!$cache_config['l1_eligible']) {
            return false;
        }
        
        // 检查数据大小（大于10KB不适合L1缓存）
        $data_size = strlen(serialize($value));
        if ($data_size > 10240) {
            return false;
        }
        
        return true;
    }
}
```

---

## 🗃️ 数据库工具合并方案 (优先级：🟡 中等)

### 合并Database_Helper + Database_Index_Manager + Database_Index_Optimizer

```php
<?php
declare(strict_types=1);

namespace NTWP\Infrastructure;

/**
 * 统一数据库管理器
 * 
 * 合并功能：
 * ✅ 基础数据库操作 (from Database_Helper)
 * ✅ 索引管理 (from Database_Index_Manager)
 * ✅ 性能优化 (from Database_Index_Optimizer)
 */
class Database_Manager {
    
    private wpdb $wpdb;
    private Performance_Monitor $monitor;
    private array $query_stats = [];
    
    public function __construct(Performance_Monitor $monitor) {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->monitor = $monitor;
    }
    
    /**
     * 批量获取Notion页面对应的WordPress文章ID
     * 
     * @param array $notion_ids Notion页面ID数组
     * @return array [notion_id => post_id] 映射
     */
    public function batch_get_posts_by_notion_ids(array $notion_ids): array {
        if (empty($notion_ids)) {
            return [];
        }
        
        $start_time = microtime(true);
        
        // 构建优化的查询
        $placeholders = implode(',', array_fill(0, count($notion_ids), '%s'));
        $query = $this->wpdb->prepare(
            "SELECT pm.meta_value as notion_id, pm.post_id
            FROM {$this->wpdb->postmeta} pm
            INNER JOIN {$this->wpdb->posts} p ON p.ID = pm.post_id
            WHERE pm.meta_key = %s
            AND pm.meta_value IN ($placeholders)
            AND p.post_status != 'trash'",
            '_notion_page_id',
            ...$notion_ids
        );
        
        $results = $this->wpdb->get_results($query);
        
        // 记录查询性能
        $this->record_query_performance($query, microtime(true) - $start_time);
        
        // 处理结果
        $mapping = array_fill_keys($notion_ids, 0);
        foreach ($results as $row) {
            $mapping[$row->notion_id] = intval($row->post_id);
        }
        
        return $mapping;
    }
    
    /**
     * 优化数据库索引
     */
    public function optimize_indexes(): void {
        // 检查必要的索引
        $this->ensure_notion_indexes();
        
        // 优化现有索引
        $this->optimize_existing_indexes();
        
        // 清理无用索引
        $this->cleanup_unused_indexes();
    }
    
    /**
     * 获取性能统计
     */
    public function get_performance_stats(): array {
        return [
            'total_queries' => $this->wpdb->num_queries,
            'custom_queries' => count($this->query_stats),
            'average_query_time' => $this->calculate_average_query_time(),
            'slow_queries' => $this->get_slow_queries(),
            'index_status' => $this->get_index_status(),
        ];
    }
    
    // ... 更多数据库管理方法
}
```

---

## 📋 合并进度跟踪表

### 总体进度

| 合并任务 | 预计工期 | 当前状态 | 完成度 | 备注 |
|---------|---------|---------|--------|------|
| **并发管理合并** | 6天 | 📋 计划中 | 0% | 最高优先级 |
| **缓存系统合并** | 4天 | 📋 计划中 | 0% | 中等优先级 |
| **数据库工具合并** | 3天 | 📋 计划中 | 0% | 中等优先级 |
| **测试和验证** | 2天 | 📋 计划中 | 0% | 质量保证 |

### 详细任务分解

#### 并发管理合并任务
- [ ] **第1天**：创建 `Concurrency_Manager` 基础结构
- [ ] **第2天**：迁移 `Concurrent_Network_Manager` 的cURL逻辑
- [ ] **第3天**：迁移 `Unified_Concurrency_Manager` 的配置管理
- [ ] **第4天**：迁移 `Dynamic_Concurrency_Manager` 的调优算法
- [ ] **第5天**：集成测试和性能验证
- [ ] **第6天**：更新调用点和清理旧代码

#### 缓存系统合并任务
- [ ] **第1天**：设计多层缓存架构
- [ ] **第2天**：实现L1内存缓存逻辑
- [ ] **第3天**：实现L2持久化缓存逻辑
- [ ] **第4天**：集成测试和性能优化

#### 数据库工具合并任务
- [ ] **第1天**：创建 `Database_Manager` 基础功能
- [ ] **第2天**：合并索引管理和优化功能
- [ ] **第3天**：测试和性能验证

---

## 🎯 预期收益评估

### 代码质量改善

| 指标 | 合并前 | 合并后 | 改善幅度 |
|-----|-------|-------|---------|
| **代码重复率** | 34% | 8% | ↓ 76% |
| **类的数量** | 8个重复类 | 3个统一类 | ↓ 62% |
| **代码行数** | ~2100行 | ~1200行 | ↓ 43% |
| **维护复杂度** | 高 | 低 | ↓ 60% |

### 性能提升预期

| 性能指标 | 当前表现 | 预期改善 | 改善原因 |
|---------|---------|---------|---------|
| **并发处理效率** | 基线 | ↑ 40% | 统一优化策略 |
| **缓存命中率** | 65% | 85% | 多层缓存架构 |
| **数据库查询速度** | 基线 | ↑ 25% | 优化索引策略 |
| **内存使用** | 基线 | ↓ 20% | 减少重复对象 |

### 开发效率提升

| 开发活动 | 改善幅度 | 具体收益 |
|---------|---------|---------|
| **新功能开发** | ↑ 50% | 统一接口，减少学习成本 |
| **Bug修复** | ↑ 60% | 集中逻辑，易于定位问题 |
| **代码审查** | ↑ 40% | 减少重复代码审查 |
| **测试编写** | ↑ 45% | 统一测试策略 |

---

## ⚠️ 风险管控

### 高风险项识别

#### 1. 并发管理合并风险 🔴
**风险类型：** 功能回归风险
**具体风险：**
- cURL multi-handle逻辑迁移可能影响网络请求稳定性
- 动态调优算法变更可能导致性能下降
- 配置管理变更可能影响现有配置兼容性

**缓解措施：**
- [ ] 建立完整的性能基准测试
- [ ] 实施金丝雀发布策略
- [ ] 保留旧类作为紧急回滚选项
- [ ] 增加详细的监控和报警

#### 2. 缓存系统合并风险 🟡
**风险类型：** 数据一致性风险
**具体风险：**
- L1和L2缓存同步可能出现不一致
- 缓存失效策略变更可能影响数据准确性

**缓解措施：**
- [ ] 实施严格的缓存一致性测试
- [ ] 建立缓存状态监控机制
- [ ] 提供手动缓存清理工具

### 回滚计划

#### 快速回滚策略
```php
// 紧急回滚：恢复旧类的依赖注入
if (defined('NTWP_EMERGENCY_ROLLBACK') && NTWP_EMERGENCY_ROLLBACK) {
    // 恢复旧的并发管理类
    self::register('concurrency', function() {
        return new \NTWP\Utils\Unified_Concurrency_Manager();
    });
    
    // 恢复旧的缓存类
    self::register('cache', function() {
        return new \NTWP\Utils\Smart_Cache();
    });
}
```

---

## 📈 成功验收标准

### 功能验收标准
- [ ] 所有原有功能保持100%兼容
- [ ] 新的统一接口通过所有单元测试
- [ ] 性能基准测试达到预期改善目标
- [ ] 代码覆盖率保持在85%以上

### 性能验收标准  
- [ ] 并发处理效率提升≥30%
- [ ] 缓存命中率提升≥15%
- [ ] 内存使用量减少≥15%
- [ ] 代码重复率降低≥50%

### 质量验收标准
- [ ] 代码审查通过率100%
- [ ] 静态分析工具无严重警告
- [ ] 文档更新完整准确
- [ ] 团队培训完成并通过考核

---

## 🎉 总结

通过系统性的类合并方案，我们将实现：

1. **架构简化**：从8个重复类合并为3个统一类
2. **性能优化**：统一优化策略，提升整体效能
3. **维护便利**：集中管理，降低维护复杂度
4. **开发效率**：统一接口，提升开发体验

这个合并方案是整个重构计划的重要组成部分，将为后续的架构升级奠定坚实基础。